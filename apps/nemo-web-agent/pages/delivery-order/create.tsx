import { withAuth } from '@/hocs'
import { FC, ReactNode, useEffect, useMemo, useState } from 'react'
import { useRouter } from 'next/router'
import { useShallow } from 'zustand/react/shallow'
import { ParsedUrlQueryInput } from 'querystring'
import { Model, ChoicesRestfull, FunctionFactory, settings } from 'survey-core'
import { Survey } from 'survey-react-ui'
import { getFirebaseToken, getInitialHeader } from '@/services/auth'
import { senderSelectForm, theme } from '@/survey-forms'
import { isPhoneNumberValid } from '@/utils/survey/validator'
import 'survey-core/defaultV2.min.css'
import { Button, DataTable, GroupSection, Paginator } from 'ui'
import { LayoutPage } from '@/components/layout'
import { useTranslation } from '@/hooks'
import { usePersistStore } from '@/stores/persistStore'
import { useJobStore } from '@/stores/jobStore'
import { DOJobListField } from '@/constants/delivery-order'
import { handleQueryOrderBy, scrollToFirstError } from '@/utils'
import { useListQueryContext } from '@/hooks/useListQueryContext'
import { ConnectionState } from 'utils/models/connectionState'
import { DeliveryOrder } from '@/services/models/deliveryOrder'
import { useDeliveryOrderStore } from '@/stores/deliveryOrderStore'
import { useSystemStore } from '@/stores/systemStore'
import { useUserStore } from '@/stores/userStore'
import { ShopMenuSlug } from 'ui/src/models'

FunctionFactory.Instance.register('isPhoneNumberValid', isPhoneNumberValid)

const CreateDeliveryOrderPage: FC = () => {
  const router = useRouter()
  const { t } = useTranslation('common')
  const [surveyModel, setSurveyModel] = useState<Model>()
  const [editingDODetail, setEditingDODetail] = useState<DeliveryOrder>(DeliveryOrder.toEmptyDODetail())
  const selectedJobCount = editingDODetail.jobIds.length
  const { jobs, pagination, connectionState, fetchJobs, isEmpty } = useJobStore(
    useShallow(state => ({
      pagination: state.pagination,
      jobs: state.jobs,
      connectionState: state.connectionState,
      fetchJobs: state.fetchJobs,
      isEmpty: state.isEmpty,
    }))
  )

  const { createDO } = useDeliveryOrderStore(
    useShallow(state => ({
      createDO: state.createDeliveryOrder,
    }))
  )

  const { currentBranchName } = usePersistStore(
    useShallow(state => {
      return {
        currentBranchName: state.currentBranchName,
      }
    })
  )

  const { showCommonModal, hideCommonModal } = useSystemStore(
    useShallow(state => {
      return {
        showCommonModal: state.showCommonModal,
        hideCommonModal: state.hideCommonModal,
      }
    })
  )

  const { currentUser } = useUserStore(
    useShallow(state => {
      return {
        currentUser: state.user,
      }
    })
  )

  const {
    getQueryContext,
    getDefaultPagination,
    getCurrentPaginationFromQuery,
    onPaginationChanged,
    onSortingChanged,
  } = useListQueryContext(router, pagination)

  const onDODetailChange = (fieldName: any, value: any) => {
    setEditingDODetail({ ...editingDODetail, [fieldName]: value })
  }

  const initSurveyAuth = async (): Promise<void> => {
    const token = await getFirebaseToken()
    ChoicesRestfull.clearCache()
    ChoicesRestfull.onBeforeSendRequest = (sender, options) => {
      const headers = getInitialHeader()
      options.request.setRequestHeader('Authorization', 'Bearer ' + token)
      options.request.setRequestHeader('x-branch', headers['x-branch'])
      options.request.setRequestHeader('x-company', headers['x-company'])
      options.request.setRequestHeader('Content-Type', 'application/json')
    }
    settings.web.cacheLoadedChoices = false
  }

  useEffect(() => {
    initSurveyAuth().then(() => {
      const survey = new Model(senderSelectForm)

      survey.applyTheme(theme)
      survey.hideRequiredErrors = true
      survey.showNavigationButtons = false
      // Refuse to type text
      survey.textUpdateMode = 'onTyping'
      // hide question name emptyField
      survey.onAfterRenderQuestion.add(function (sender, options) {
        if (options.question.name === 'emptyField') {
          options.htmlElement.style.opacity = '0'
          options.htmlElement.style.height = '0px'
        }
      })
      survey
        .getAllQuestions()
        .forEach(question => (question.cssClasses.hintSuffix = `${question.cssClasses.hintSuffix} !font-sans`))
      const senderQuestion = survey.getQuestionByName('senderUserKey')
      senderQuestion.value = currentUser?.userKey
      onDODetailChange('senderUserKey', currentUser?.userKey)
      setSurveyModel(survey)
    })
  }, [currentUser])

  useEffect(() => {
    if (router.isReady) {
      fetchJobs(getQueryContext(), false, 'purchased', true, 'new')
    }
  }, [router.query])

  const onSurveyChange = async (sender: Model, options: any) => {
    if (options.name === 'senderMobileNumber') {
      // Remove non-numeric characters
      const tel = options.value.replace(/\D/g, '').slice(0, 10)
      options.value = tel

      // Update the survey data with the cleaned value
      sender.setValue(options.name, options.value)
    }
    onDODetailChange(options.name, options.value)
  }

  const onValidate = (): boolean => {
    let isValid: boolean = true

    const validate = surveyModel?.validate() || false
    if (!validate) {
      isValid = validate
    }
    scrollToFirstError()

    return isValid
  }

  const onCreateDO = async () => {
    if (!onValidate()) {
      return
    }
    handleCreateDOModal()
  }

  const handleCancel = () => {
    showCommonModal({
      title: t('do-management.cancel-create-title'),
      description: t('do-management.cancel-create-desc'),
      onClose: () => hideCommonModal(),
      onClickNegativeButton: () => hideCommonModal(),
      onClickPositiveButton: async () => {
        hideCommonModal()
        router.push({
          pathname: `/${ShopMenuSlug.deliveryOrder}`,
        })
      },
      buttonType: 'horizontal-duo',
      positiveButtonTxt: t('confirm'),
      negativeButtonTxt: t('back'),
      icon: 'ic-warning',
      iconCls: '!h-16 !w-16',
    })
  }

  const handleCreateDOModal = () => {
    showCommonModal({
      title: t('do-management.confirm-create-title'),
      description: t('do-management.confirm-create-desc'),
      onClose: () => hideCommonModal(),
      onClickNegativeButton: () => hideCommonModal(),
      onClickPositiveButton: async () => {
        hideCommonModal()
        const DOParams = DeliveryOrder.toParams(editingDODetail)
        const result = await createDO(DOParams)
        if (result?.deliveryOrderId) {
          router.push({
            pathname: `/${ShopMenuSlug.deliveryOrder}/detail`,
            query: { status: 'success', id: result.deliveryOrderId },
          })
        }
      },
      buttonType: 'horizontal-duo',
      positiveButtonTxt: t('confirm'),
      negativeButtonTxt: t('back'),
      icon: 'ic-task-document-blue',
      iconCls: '!h-16 !w-16',
    })
  }

  const handleExitCreate = (pathname: string, query?: ParsedUrlQueryInput) => {
    showCommonModal({
      title: t('do-management.exit-create-title'),
      description: t('do-management.cancel-create-desc'),
      onClose: () => hideCommonModal(),
      onClickNegativeButton: () => hideCommonModal(),
      onClickPositiveButton: async () => {
        hideCommonModal()
        router.push({ pathname, query })
      },
      buttonType: 'horizontal-duo',
      positiveButtonTxt: t('confirm'),
      negativeButtonTxt: t('back'),
      icon: 'ic-warning',
      iconCls: '!h-16 !w-16',
    })
  }

  const customBreadcrumb: ReactNode = useMemo(() => {
    return (
      <div className="flex flex-row justify-center mb-4">
        <span
          className="text-center cursor-pointer text-primary-500 text-b4-regular"
          onClick={() => handleExitCreate(`/${ShopMenuSlug.deliveryOrder}`)}
        >
          {t('do-list')}
        </span>
        <div className="mr-2 icons ic-chevron-next-black" />
        <span className="cursor-pointer text-base-700 text-b4-regular">{t('do-management.create-do')}</span>
      </div>
    )
  }, [router.query.id])

  return (
    <LayoutPage
      pageTitle={{
        title: t('do-management.create-do'),
        customBreadcrumb,
      }}
      subFooter={undefined}
    >
      <div className="w-full mt-4 mb-4 border-2 rounded-lg bg-base-25">
        <div className="flex items-center justify-between p-4 shadow-md shadow-[#3B5998]/10 rounded-b-lg">
          <div className="flex items-center justify-center mr-2 rounded-lg w-11 h-11 bg-positive-100">
            <i className="icons ic-location-pin-green !w-[36px] !h-[36px]" />
          </div>
          <div className="w-full">
            <p className="text-t5-semi-bold">{t('do-management.pickup-location')}</p>
            <p className="text-b4-regular text-base-500">{currentBranchName}</p>
          </div>
        </div>
        <div className="w-full p-4">
          <p className="mb-4 text-h4-bold text-primary-500">
            {`${t('do-management.select-ready-job')}`}{' '}
            <span className={`${selectedJobCount > 20 && 'text-negative-500 --error'}`}>{`(${selectedJobCount}/20 ${t(
              'do-management.list'
            )})`}</span>
          </p>
          <DataTable
            fields={DOJobListField(t, false)}
            data={jobs}
            getId={(data: any) => data?.jobId}
            sorting={handleQueryOrderBy(router) ? handleQueryOrderBy(router) : undefined}
            toggleSort={(fieldName: string) => onSortingChanged(fieldName)}
            action={{
              hideAction: true,
            }}
            isLoading={ConnectionState.isLoading(connectionState)}
            isEmptyView={isEmpty() || ConnectionState.isError(connectionState)}
            emptyView={{
              image: <div className="images !h-[150px] img-empty-view" />,
              title: t('do-management.empty-ready-job'),
              hideIcon: true,
            }}
            heightOverScreen={true}
            cls={`!static ${ConnectionState.isLoading(connectionState) ? 'min-h-[470px]' : ''}`}
            t={t}
            selector={{
              onSelect: (value: any[]) => onDODetailChange('jobIds', value),
              selectedValues: editingDODetail.jobIds,
              selectable: 'multi',
              selectArea: 'choice',
              sticky: true,
            }}
          />
          {ConnectionState.isSuccess(connectionState) && !isEmpty() && (
            <div className="pt-8">
              <Paginator
                currentPage={pagination.page || 1}
                currentPageSizeSelected={{
                  label: getCurrentPaginationFromQuery().pageSize.toString(),
                  value: getCurrentPaginationFromQuery().pageSize.toString(),
                }}
                defaultPageSize={getDefaultPagination().pageSize || 10}
                totalItem={pagination?.totalRecords ?? 0}
                onChangePagination={(page: number) => onPaginationChanged(pagination.assignPage(page))}
                t={t}
                hideGotoPage
              />
            </div>
          )}
        </div>
      </div>
      dd
      {surveyModel && (
        <GroupSection isRounded={true} title={t('do-management.sender-detail')}>
          <div className="mt-4">
            <Survey model={surveyModel} onValueChanged={onSurveyChange} />
          </div>
        </GroupSection>
      )}
      <footer className="w-full">
        <div className="flex justify-between mt-4">
          <Button colorScheme="primary" variant="outlined" cls={`h-[48px] w-[168px]`} onClick={handleCancel}>
            {t('jobs-management.cancel-job')}
          </Button>
          <div className="flex gap-2">
            <Button
              colorScheme="primary"
              variant="filled"
              cls={`h-[48px] w-[168px]`}
              isDisabled={!selectedJobCount || selectedJobCount > 20}
              onClick={onCreateDO}
            >
              {t('do-management.create-do')}
            </Button>
          </div>
        </div>
      </footer>
    </LayoutPage>
  )
}

export default withAuth(CreateDeliveryOrderPage)
