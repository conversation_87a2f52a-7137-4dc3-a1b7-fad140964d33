import { useState, useMemo, useRef, useEffect } from 'react'

type IChoice = { [key: string]: any }

export interface ISmartDropdownSimpleProps {
  selected: IChoice
  setSelection: (item: any) => void
  choices: IChoice[]
  valueField?: string
  textField?: string
  label?: string
  isRequire?: boolean
  t: (message: string, obj?: Record<string, any>) => string
  onClickDropdown?: (toStatus: boolean) => void
  mountDropdownOpen?: boolean
  disabled?: boolean
  truncateSelected?: boolean
  clsSelected?: string
  smartPosition?: 'top' | 'bottom'
}

/**
 * SmartDropdownSimple - Enhanced version of DropdownSimple with smart positioning
 * Automatically detects screen boundaries and positions dropdown accordingly
 */
export function SmartDropdownSimple({
  selected,
  setSelection,
  choices,
  valueField = 'value',
  textField = 'text',
  label,
  isRequire = false,
  t,
  onClickDropdown,
  mountDropdownOpen = false,
  disabled,
  truncateSelected,
  clsSelected,
  smartPosition = 'bottom',
}: Readonly<ISmartDropdownSimpleProps>) {
  const refDropdownContainer = useRef<HTMLDivElement | null>(null)
  const [openDropdown, setOpenDropdown] = useState<boolean>(false)

  const { dropdownChoices, placeholderValue } = useMemo(() => {
    if (choices.length) {
      const placeholderValue = !choices[0]?.[valueField] ? choices[0] : undefined
      const dropdownChoices = !placeholderValue ? choices : choices.slice(1)
      return { dropdownChoices, placeholderValue }
    }
    return { dropdownChoices: [], placeholderValue: undefined }
  }, [choices, valueField])

  useEffect(() => {
    if (mountDropdownOpen) {
      setOpenDropdown(mountDropdownOpen)
    }
  }, [])

  // Smart positioning styles
  const dropdownStyles = useMemo(() => {
    if (smartPosition === 'top') {
      return {
        position: 'absolute' as const,
        bottom: '100%',
        top: 'auto',
        left: '0',
        right: '0',
        transform: 'translateY(-8px)',
        borderRadius: '0.5rem 0.5rem 0 0',
        zIndex: 50,
      }
    }
    return {}
  }, [smartPosition])

  return (
    <div ref={refDropdownContainer} className="w-full z-0">
      {label && (
        <div>
          <p className="text-b4-regular text-base-700">
            {label}
            {isRequire && <span className="text-negative-500"> *</span>}
          </p>
        </div>
      )}
      <div className={`mt-1 ${openDropdown && 'shadow-md rounded-lg'} ${disabled && 'bg-base-100 rounded-lg'}`}>
        <button
          onClick={() =>
            setOpenDropdown(prev => {
              onClickDropdown && onClickDropdown(!prev)
              return !prev
            })
          }
          disabled={disabled}
          className="w-full"
        >
          <div
            className={`flex flex-row justify-between items-center w-full border-base-200 border-[1px] border-solid rounded-t-lg ${
              !openDropdown && 'rounded-b-lg'
            } px-4 py-[10px]`}
          >
            <p
              className={`${
                (placeholderValue?.[valueField] as string) === selected[valueField] ? 'text-base-500' : 'text-base-700'
              } ${disabled && '!text-base-400'} ${truncateSelected && 'truncate'} ${clsSelected}`}
            >
              {t(selected?.[textField] || '')}
            </p>
            <i className={`icons ${disabled ? 'ic-dropdown-disabled !w-[10px] !h-[5px]' : 'ic-dropdown-black'}`} />
          </div>
        </button>
        {openDropdown && (
          <div
            className={`flex flex-col w-full max-h-[120px] overflow-y-scroll py-1 z-2 bg-white border-base-200 border-[1px] border-solid ${
              smartPosition === 'top' ? 'rounded-t-lg' : 'rounded-b-lg'
            }`}
            style={dropdownStyles}
          >
            {dropdownChoices.map((item: any) => (
              <button
                key={item[valueField].toString()}
                onClick={() => {
                  setSelection(item)
                  setOpenDropdown(false)
                }}
                className="w-full"
              >
                <div
                  className={`ltr mx-1 px-3 py-[10px] ${
                    item[valueField] === selected[valueField] && 'bg-primary-500 text-white'
                  } rounded-lg`}
                >
                  <p className="text-left ">{t(item?.[textField] || '')}</p>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
