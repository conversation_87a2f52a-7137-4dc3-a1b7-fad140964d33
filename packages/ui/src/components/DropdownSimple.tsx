import { useState, useMemo, useRef, useEffect } from 'react'

type IChoice = { [key: string]: any }

export interface IDropdownSimpleProps {
  selected: IChoice
  setSelection: (item: any) => void
  choices: IChoice[]
  valueField?: string
  textField?: string
  label?: string
  isRequire?: boolean
  t: (message: string, obj?: Record<string, any>) => string
  onClickDropdown?: (toStatus: boolean) => void
  mountDropdownOpen?: boolean
  absoluteChoice?: boolean
  parentLeftPx?: number
  disabled?: boolean
  truncateSelected?: boolean
  clsSelected?: string
  isAutoCalculatePosition?: boolean
}

/**
 * DropdownSimple
 * - Choices - interface { [key: string]: any } for flexible label and value field config
 *    - choices [{valueField, textField}]
 *    - valueField
 *    - textField
 * - placeholder value - non selectable first choice
 *    - index 0 which valueField truth value is false will be placeholder value
 *    - if index 0 valueField truth value is true there is no placeholder value
 * - selected - item from choice
 * - setSelection - receive item from choice as props (must set to selected state)
 * - label & isRequire - for field label like input box label
 * - onClickDropdown - function to call when dropdown button is click from parent
 * - mountDropdownOpen - dropdown open by default or not - default is close
 * - absoluteChoice - is choice of dropdown is absolute position - default is false
 *    - parentLeftPx - for absolute position left position is detect position in parent component. in some case there is position of parent etc - this is magic number for those cases
 */
export function DropdownSimple({
  selected,
  setSelection,
  choices,
  valueField = 'value',
  textField = 'text',
  label,
  isRequire = false,
  t,
  onClickDropdown,
  mountDropdownOpen = false,
  absoluteChoice = false,
  parentLeftPx = 0,
  disabled,
  truncateSelected,
  clsSelected,
  isAutoCalculatePosition = false,
}: Readonly<IDropdownSimpleProps>) {
  const refDropdownContainer = useRef<HTMLDivElement | null>(null)

  const [openDropdown, setOpenDropdown] = useState<boolean>(false)
  const [shouldPositionTop, setShouldPositionTop] = useState<boolean>(false)

  const { dropdownChoices, placeholderValue } = useMemo(() => {
    if (choices.length) {
      const placeholderValue = !choices[0]?.[valueField] ? choices[0] : undefined
      const dropdownChoices = !placeholderValue ? choices : choices.slice(1)
      return { dropdownChoices, placeholderValue }
    }
    return { dropdownChoices: [], placeholderValue: undefined }
  }, [choices, valueField])

  const absoluteChoiceWidth = useMemo(() => {
    if (refDropdownContainer.current !== null && absoluteChoice) {
      return refDropdownContainer.current.offsetWidth
    } else {
      return undefined
    }
  }, [refDropdownContainer.current])

  const position = useMemo(() => {
    const x = refDropdownContainer.current?.getBoundingClientRect().x ?? parentLeftPx
    return x - parentLeftPx
  }, [refDropdownContainer.current?.getBoundingClientRect().x])

  const calculatePosition = () => {
    if (isAutoCalculatePosition && refDropdownContainer.current) {
      const rect = refDropdownContainer.current.getBoundingClientRect()
      const viewportHeight = window.innerHeight
      const spaceBelow = viewportHeight - rect.bottom
      const spaceAbove = rect.top
      const estimatedDropdownHeight = 200 // Estimated height for dropdown options

      if (spaceBelow < estimatedDropdownHeight && spaceAbove > estimatedDropdownHeight) {
        setShouldPositionTop(true)
      } else {
        setShouldPositionTop(false)
      }
    }
  }

  useEffect(() => {
    if (mountDropdownOpen) {
      setOpenDropdown(mountDropdownOpen)
    }
  }, [])

  return (
    <div ref={refDropdownContainer} className={`w-full z-0 ${isAutoCalculatePosition ? 'relative' : ''}`}>
      {label && (
        <div>
          <p className="text-b4-regular text-base-700">
            {label}
            {isRequire && <span className="text-negative-500"> *</span>}
          </p>
        </div>
      )}
      <div className={`mt-1 ${openDropdown && 'shadow-md rounded-lg'} ${disabled && 'bg-base-100 rounded-lg'}`}>
        <button
          onClick={() =>
            setOpenDropdown(prev => {
              const newState = !prev
              if (newState) {
                setTimeout(calculatePosition, 0)
              }
              onClickDropdown && onClickDropdown(newState)
              return newState
            })
          }
          disabled={disabled}
          className="w-full"
        >
          <div
            className={`flex flex-row justify-between items-center w-full border-base-200 border-[1px] border-solid rounded-t-lg ${
              !openDropdown && 'rounded-b-lg'
            } px-4 py-[10px]`}
          >
            <p
              className={`${
                (placeholderValue?.[valueField] as string) === selected[valueField] ? 'text-base-500' : 'text-base-700'
              } ${disabled && '!text-base-400'} ${truncateSelected && 'truncate'} ${clsSelected}`}
            >
              {t(selected?.[textField] || '')}
            </p>
            <i className={`icons ${disabled ? 'ic-dropdown-disabled !w-[10px] !h-[5px]' : 'ic-dropdown-black'}`} />
          </div>
        </button>
        {openDropdown && (
          <div
            className={`flex flex-col w-full max-h-[120px] overflow-y-scroll py-1 z-2 ${
              shouldPositionTop ? 'rounded-t-lg' : 'rounded-b-lg'
            } ${
              isAutoCalculatePosition
                ? 'bg-white border-base-200 border-[1px] border-solid'
                : absoluteChoice
                  ? 'absolute bg-white border-base-200 border-[1px] border-solid'
                  : ''
            }`}
            style={{
              ...(isAutoCalculatePosition && shouldPositionTop
                ? {
                    position: 'absolute',
                    bottom: '100%',
                    top: 'auto',
                    left: '0',
                    width: '100%',
                    zIndex: 50,
                  }
                : isAutoCalculatePosition && !shouldPositionTop
                  ? {
                      position: 'absolute',
                      top: '100%',
                      bottom: 'auto',
                      left: '0',
                      width: '100%',
                      zIndex: 50,
                    }
                  : absoluteChoice
                    ? {
                        width: absoluteChoiceWidth,
                        left: position,
                      }
                    : {}),
            }}
          >
            {dropdownChoices.map((item: any) => (
              <button
                key={item[valueField].toString()}
                onClick={() => {
                  setSelection(item)
                  setOpenDropdown(false)
                }}
                className="w-full"
                style={{ width: absoluteChoiceWidth }}
              >
                <div
                  className={`ltr mx-1 px-3 py-[10px] ${
                    item[valueField] === selected[valueField] && 'bg-primary-500 text-white'
                  } rounded-lg`}
                >
                  <p className="text-left ">{t(item?.[textField] || '')}</p>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
